class MinimalAgentApp {
    constructor() {
        this.currentConversationId = null;
        this.conversations = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadConversations();
    }

    bindEvents() {
        // 新建对话按钮
        document.getElementById('new-chat-btn').addEventListener('click', () => {
            this.createNewConversation();
        });

        // 发送消息按钮
        document.getElementById('send-btn').addEventListener('click', () => {
            this.sendMessage();
        });

        // 删除对话按钮
        document.getElementById('delete-chat-btn').addEventListener('click', () => {
            this.deleteCurrentConversation();
        });

        // 输入框回车发送
        const messageInput = document.getElementById('message-input');
        messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // 自动调整输入框高度
        messageInput.addEventListener('input', () => {
            this.adjustTextareaHeight(messageInput);
        });
    }

    adjustTextareaHeight(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }

    async loadConversations() {
        try {
            const response = await fetch('/api/conversations');
            const data = await response.json();
            
            if (data.success) {
                this.conversations = data.data;
                this.renderConversationsList();
            } else {
                console.error('Failed to load conversations:', data.error);
            }
        } catch (error) {
            console.error('Error loading conversations:', error);
        }
    }

    renderConversationsList() {
        const listContainer = document.getElementById('conversations-list');
        
        if (this.conversations.length === 0) {
            listContainer.innerHTML = `
                <div style="text-align: center; padding: 20px; color: #6c757d;">
                    <i class="fas fa-comments" style="font-size: 2rem; margin-bottom: 10px;"></i>
                    <p>还没有对话</p>
                    <p style="font-size: 12px;">点击上方按钮创建新对话</p>
                </div>
            `;
            return;
        }

        listContainer.innerHTML = this.conversations.map(conv => `
            <div class="conversation-item ${conv.id === this.currentConversationId ? 'active' : ''}" 
                 data-id="${conv.id}">
                <div class="conversation-title">
                    ${conv.title || '新对话'}
                </div>
                <div class="conversation-preview">
                    ${conv.last_message || '暂无消息'}
                </div>
                <div class="conversation-time">
                    ${this.formatTime(conv.updated_at)}
                </div>
            </div>
        `).join('');

        // 绑定点击事件
        listContainer.querySelectorAll('.conversation-item').forEach(item => {
            item.addEventListener('click', () => {
                const conversationId = item.dataset.id;
                this.selectConversation(conversationId);
            });
        });
    }

    async createNewConversation() {
        try {
            const response = await fetch('/api/conversations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    title: null
                })
            });

            const data = await response.json();
            
            if (data.success) {
                await this.loadConversations();
                this.selectConversation(data.data);
            } else {
                console.error('Failed to create conversation:', data.error);
                alert('创建对话失败: ' + data.error);
            }
        } catch (error) {
            console.error('Error creating conversation:', error);
            alert('创建对话时发生错误');
        }
    }

    async selectConversation(conversationId) {
        this.currentConversationId = conversationId;
        
        // 更新UI状态
        this.updateChatHeader();
        this.showChatInput();
        
        // 重新渲染对话列表以更新选中状态
        this.renderConversationsList();
        
        // 加载消息历史
        await this.loadMessages();
    }

    updateChatHeader() {
        const conversation = this.conversations.find(c => c.id === this.currentConversationId);
        const chatTitle = document.getElementById('chat-title');
        const deleteBtn = document.getElementById('delete-chat-btn');
        
        if (conversation) {
            chatTitle.textContent = conversation.title || '新对话';
            deleteBtn.style.display = 'block';
        }
    }

    showChatInput() {
        document.getElementById('chat-input-container').style.display = 'block';
        document.querySelector('.welcome-message').style.display = 'none';
    }

    async loadMessages() {
        if (!this.currentConversationId) return;

        try {
            const response = await fetch(`/api/conversations/${this.currentConversationId}/messages`);
            const data = await response.json();
            
            if (data.success) {
                this.renderMessages(data.data);
            } else {
                console.error('Failed to load messages:', data.error);
            }
        } catch (error) {
            console.error('Error loading messages:', error);
        }
    }

    renderMessages(messages) {
        const messagesContainer = document.getElementById('chat-messages');
        
        if (messages.length === 0) {
            messagesContainer.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #6c757d;">
                    <i class="fas fa-comment-dots" style="font-size: 3rem; margin-bottom: 15px;"></i>
                    <p>开始您的对话吧！</p>
                </div>
            `;
            return;
        }

        messagesContainer.innerHTML = messages.map(msg => this.renderMessage(msg)).join('');
        this.scrollToBottom();
    }

    renderMessage(message) {
        const isUser = message.role === 'user';
        const avatar = isUser ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';
        
        let toolCallsHtml = '';
        if (message.tool_calls && message.tool_calls.length > 0) {
            toolCallsHtml = `
                <div class="tool-calls">
                    <strong>🔧 工具调用:</strong>
                    ${message.tool_calls.map(call => `
                        <div class="tool-call">
                            <span class="tool-call-name">${call.name}</span>
                            <div class="tool-call-result">${JSON.stringify(call.parameters, null, 2)}</div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        return `
            <div class="message ${message.role}">
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">
                    <div class="message-text">${this.escapeHtml(message.content)}</div>
                    ${toolCallsHtml}
                    <div class="message-time">${this.formatTime(message.timestamp)}</div>
                </div>
            </div>
        `;
    }

    async sendMessage() {
        const input = document.getElementById('message-input');
        const content = input.value.trim();
        
        if (!content || !this.currentConversationId) return;

        // 清空输入框并禁用发送按钮
        input.value = '';
        this.adjustTextareaHeight(input);
        this.setLoading(true);

        try {
            // 立即显示用户消息
            this.addMessageToUI({
                id: Date.now().toString(),
                role: 'user',
                content: content,
                timestamp: new Date().toISOString()
            });

            const response = await fetch(`/api/conversations/${this.currentConversationId}/messages`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    content: content
                })
            });

            const data = await response.json();
            
            if (data.success) {
                // 显示助手回复
                this.addMessageToUI(data.data);
                
                // 更新对话列表
                await this.loadConversations();
            } else {
                console.error('Failed to send message:', data.error);
                alert('发送消息失败: ' + data.error);
            }
        } catch (error) {
            console.error('Error sending message:', error);
            alert('发送消息时发生错误');
        } finally {
            this.setLoading(false);
        }
    }

    addMessageToUI(message) {
        const messagesContainer = document.getElementById('chat-messages');
        
        // 如果是第一条消息，清除欢迎内容
        if (messagesContainer.querySelector('.welcome-message') || 
            messagesContainer.children.length === 0) {
            messagesContainer.innerHTML = '';
        }
        
        messagesContainer.insertAdjacentHTML('beforeend', this.renderMessage(message));
        this.scrollToBottom();
    }

    async deleteCurrentConversation() {
        if (!this.currentConversationId) return;

        if (!confirm('确定要删除这个对话吗？此操作无法撤销。')) {
            return;
        }

        try {
            const response = await fetch(`/api/conversations/${this.currentConversationId}`, {
                method: 'DELETE'
            });

            const data = await response.json();
            
            if (data.success) {
                // 重置当前对话
                this.currentConversationId = null;
                
                // 重新加载对话列表
                await this.loadConversations();
                
                // 重置UI
                this.resetChatUI();
            } else {
                console.error('Failed to delete conversation:', data.error);
                alert('删除对话失败: ' + data.error);
            }
        } catch (error) {
            console.error('Error deleting conversation:', error);
            alert('删除对话时发生错误');
        }
    }

    resetChatUI() {
        document.getElementById('chat-title').textContent = '选择或创建一个对话';
        document.getElementById('delete-chat-btn').style.display = 'none';
        document.getElementById('chat-input-container').style.display = 'none';
        
        const messagesContainer = document.getElementById('chat-messages');
        messagesContainer.innerHTML = `
            <div class="welcome-message">
                <div class="welcome-content">
                    <i class="fas fa-robot welcome-icon"></i>
                    <h3>欢迎使用 Minimal Agent</h3>
                    <p>这是一个基于 Rust 构建的智能 Agent 系统</p>
                    <p>支持工具调用和对话历史记录</p>
                    <div class="features">
                        <div class="feature">
                            <i class="fas fa-tools"></i>
                            <span>工具调用</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-history"></i>
                            <span>历史记录</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-brain"></i>
                            <span>智能推理</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setLoading(loading) {
        const overlay = document.getElementById('loading-overlay');
        const sendBtn = document.getElementById('send-btn');
        
        overlay.style.display = loading ? 'flex' : 'none';
        sendBtn.disabled = loading;
    }

    scrollToBottom() {
        const messagesContainer = document.getElementById('chat-messages');
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);

        if (diffMins < 1) return '刚刚';
        if (diffMins < 60) return `${diffMins}分钟前`;
        if (diffHours < 24) return `${diffHours}小时前`;
        if (diffDays < 7) return `${diffDays}天前`;
        
        return date.toLocaleDateString('zh-CN');
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new MinimalAgentApp();
});
