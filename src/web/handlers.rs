use crate::database::models::*;
use crate::web::state::AppState;
use crate::{Agent, Message};
use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::J<PERSON>,
};
use serde_json::json;
use tracing::{debug, error, info};

/// 获取所有对话列表
pub async fn get_conversations(
    State(state): State<AppState>,
) -> Result<Json<ApiResponse<Vec<ConversationInfo>>>, StatusCode> {
    debug!("GET /api/conversations");
    
    match state.db.get_conversations().await {
        Ok(conversations) => {
            info!("Retrieved {} conversations", conversations.len());
            Ok(Json(ApiResponse::success(conversations)))
        }
        Err(e) => {
            error!("Failed to get conversations: {}", e);
            Ok(Json(ApiResponse::error(e.to_string())))
        }
    }
}

/// 创建新对话
pub async fn create_conversation(
    State(state): State<AppState>,
    <PERSON><PERSON>(request): <PERSON><PERSON><CreateConversationRequest>,
) -> Result<Json<ApiResponse<String>>, StatusCode> {
    debug!("POST /api/conversations");
    
    match state.db.create_conversation(request.title).await {
        Ok(conversation_id) => {
            info!("Created conversation: {}", conversation_id);
            Ok(Json(ApiResponse::success(conversation_id)))
        }
        Err(e) => {
            error!("Failed to create conversation: {}", e);
            Ok(Json(ApiResponse::error(e.to_string())))
        }
    }
}

/// 获取对话的消息历史
pub async fn get_conversation_messages(
    State(state): State<AppState>,
    Path(conversation_id): Path<String>,
) -> Result<Json<ApiResponse<Vec<WebMessage>>>, StatusCode> {
    debug!("GET /api/conversations/{}/messages", conversation_id);
    
    match state.db.get_messages(&conversation_id).await {
        Ok(messages) => {
            info!("Retrieved {} messages for conversation {}", messages.len(), conversation_id);
            Ok(Json(ApiResponse::success(messages)))
        }
        Err(e) => {
            error!("Failed to get messages: {}", e);
            Ok(Json(ApiResponse::error(e.to_string())))
        }
    }
}

/// 发送消息并获取 Agent 响应
pub async fn send_message(
    State(state): State<AppState>,
    Path(conversation_id): Path<String>,
    Json(request): Json<SendMessageRequest>,
) -> Result<Json<ApiResponse<WebMessage>>, StatusCode> {
    debug!("POST /api/conversations/{}/messages", conversation_id);
    info!("Processing message: {}", request.content);
    
    // 验证对话是否存在
    match state.db.get_conversation(&conversation_id).await {
        Ok(Some(_)) => {},
        Ok(None) => {
            error!("Conversation not found: {}", conversation_id);
            return Ok(Json(ApiResponse::error("Conversation not found".to_string())));
        }
        Err(e) => {
            error!("Failed to check conversation: {}", e);
            return Ok(Json(ApiResponse::error(e.to_string())));
        }
    }

    // 创建用户消息
    let user_message = Message::new_user(request.content.clone());
    
    // 保存用户消息到数据库
    if let Err(e) = state.db.save_message(&conversation_id, &user_message).await {
        error!("Failed to save user message: {}", e);
        return Ok(Json(ApiResponse::error(e.to_string())));
    }

    // 创建 Agent 实例并处理消息
    let mut agent = Agent::new(state.agent_config.clone(), (*state.tool_registry).clone());
    agent.start_conversation();

    // 加载历史消息到 Agent
    match state.db.get_messages(&conversation_id).await {
        Ok(messages) => {
            // 将历史消息转换为 Agent 消息格式并添加到对话中
            for web_msg in messages {
                if web_msg.id != user_message.id.to_string() { // 跳过刚刚添加的用户消息
                    let agent_message = convert_web_message_to_agent_message(web_msg);
                    if let Err(e) = agent.add_message_to_current_conversation(agent_message) {
                        error!("Failed to add historical message to agent: {}", e);
                    }
                }
            }
        }
        Err(e) => {
            error!("Failed to load conversation history: {}", e);
        }
    }

    // 处理用户输入
    match agent.process_user_input(request.content).await {
        Ok(response_content) => {
            // 创建 Assistant 响应消息
            let assistant_message = Message::new_assistant(response_content);
            
            // 保存 Assistant 消息到数据库
            if let Err(e) = state.db.save_message(&conversation_id, &assistant_message).await {
                error!("Failed to save assistant message: {}", e);
                return Ok(Json(ApiResponse::error(e.to_string())));
            }

            // 转换为 Web 消息格式返回
            let web_message = WebMessage {
                id: assistant_message.id.to_string(),
                role: "assistant".to_string(),
                content: assistant_message.content,
                timestamp: assistant_message.timestamp,
                tool_calls: assistant_message.tool_calls.map(|calls| serde_json::to_value(calls).unwrap_or_default()),
                tool_call_results: assistant_message.tool_call_results.map(|results| serde_json::to_value(results).unwrap_or_default()),
            };

            info!("Successfully processed message for conversation {}", conversation_id);
            Ok(Json(ApiResponse::success(web_message)))
        }
        Err(e) => {
            error!("Agent failed to process message: {}", e);
            Ok(Json(ApiResponse::error(e.to_string())))
        }
    }
}

/// 删除对话
pub async fn delete_conversation(
    State(state): State<AppState>,
    Path(conversation_id): Path<String>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    debug!("DELETE /api/conversations/{}", conversation_id);
    
    match state.db.delete_conversation(&conversation_id).await {
        Ok(_) => {
            info!("Deleted conversation: {}", conversation_id);
            Ok(Json(ApiResponse::success(())))
        }
        Err(e) => {
            error!("Failed to delete conversation: {}", e);
            Ok(Json(ApiResponse::error(e.to_string())))
        }
    }
}

/// 健康检查端点
pub async fn health_check() -> Json<serde_json::Value> {
    Json(json!({
        "status": "healthy",
        "timestamp": chrono::Utc::now().to_rfc3339()
    }))
}

/// 将 WebMessage 转换为 Agent Message
fn convert_web_message_to_agent_message(web_msg: WebMessage) -> Message {
    use crate::types::MessageRole;
    use uuid::Uuid;
    
    let role = match web_msg.role.as_str() {
        "user" => MessageRole::User,
        "assistant" => MessageRole::Assistant,
        "system" => MessageRole::System,
        "tool" => MessageRole::Tool,
        _ => MessageRole::User,
    };

    Message {
        id: Uuid::parse_str(&web_msg.id).unwrap_or_else(|_| Uuid::new_v4()),
        role,
        content: web_msg.content,
        timestamp: web_msg.timestamp,
        tool_calls: web_msg.tool_calls.and_then(|v| serde_json::from_value(v).ok()),
        tool_call_results: web_msg.tool_call_results.and_then(|v| serde_json::from_value(v).ok()),
    }
}
