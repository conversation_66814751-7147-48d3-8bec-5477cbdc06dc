use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// 数据库中的对话记录
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct DbConversation {
    pub id: String,
    pub title: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub metadata: Option<String>, // JSON 字符串
}

/// 数据库中的消息记录
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct DbMessage {
    pub id: String,
    pub conversation_id: String,
    pub role: String,
    pub content: String,
    pub timestamp: DateTime<Utc>,
    pub tool_calls: Option<String>, // JSON 字符串
    pub tool_call_results: Option<String>, // JSON 字符串
}

/// Web API 响应的对话信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversationInfo {
    pub id: String,
    pub title: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub message_count: i64,
    pub last_message: Option<String>,
}

/// Web API 的消息格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebMessage {
    pub id: String,
    pub role: String,
    pub content: String,
    pub timestamp: DateTime<Utc>,
    pub tool_calls: Option<serde_json::Value>,
    pub tool_call_results: Option<serde_json::Value>,
}

/// 创建新对话的请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateConversationRequest {
    pub title: Option<String>,
}

/// 发送消息的请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SendMessageRequest {
    pub content: String,
}

/// API 响应格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
        }
    }

    pub fn error(error: String) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(error),
        }
    }
}

impl From<crate::types::Message> for DbMessage {
    fn from(message: crate::types::Message) -> Self {
        Self {
            id: message.id.to_string(),
            conversation_id: "".to_string(), // 需要在保存时设置
            role: format!("{:?}", message.role).to_lowercase(),
            content: message.content,
            timestamp: message.timestamp,
            tool_calls: message.tool_calls.map(|calls| serde_json::to_string(&calls).unwrap_or_default()),
            tool_call_results: message.tool_call_results.map(|results| serde_json::to_string(&results).unwrap_or_default()),
        }
    }
}

impl From<DbMessage> for WebMessage {
    fn from(db_message: DbMessage) -> Self {
        Self {
            id: db_message.id,
            role: db_message.role,
            content: db_message.content,
            timestamp: db_message.timestamp,
            tool_calls: db_message.tool_calls
                .and_then(|s| serde_json::from_str(&s).ok()),
            tool_call_results: db_message.tool_call_results
                .and_then(|s| serde_json::from_str(&s).ok()),
        }
    }
}
